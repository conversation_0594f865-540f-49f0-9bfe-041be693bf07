<script lang="ts" setup>
import ImageInfo from './ImageInfo.vue'

defineOptions({
  name: "ImageDetails"
})

interface ImageData {
  id: string
  name: string
  size: string
  format: string
  uploadTime: string
  thumbnail: string
  fabricObject?: any
}

interface Props {
  image: ImageData | null
}

defineProps<Props>()
</script>

<template>
  <div v-if="image" class="image-details">
    <ImageInfo :image="image" :show-details="true" />
  </div>
</template>

<style lang="scss" scoped>
.image-details {
  padding: 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}
</style>
