import { request } from "@/http/axios"

/**
 * 生成资源完整URL
 * @param resourcePath 资源相对路径，如 "chat/image/1945794645288161280.gif"
 * @returns 完整的资源URL
 */
export function getResourceUrl(resourcePath: string): string {
  if (!resourcePath) {
    return ''
  }

  // 如果已经是完整URL，直接返回
  if (resourcePath.startsWith('http://') || resourcePath.startsWith('https://')) {
    return resourcePath
  }

  // 如果已经包含 /resource/，直接返回
  if (resourcePath.startsWith('/resource/')) {
    return resourcePath
  }

  // 移除开头的斜杠（如果有）
  const cleanPath = resourcePath.startsWith('/') ? resourcePath.slice(1) : resourcePath

  // 拼接资源URL（不包含 /api/v1，因为 baseURL 已经包含了）
  const fullUrl = `/resource/${cleanPath}`

  return fullUrl
}

/**
 * 获取资源文件（支持图片、文档等）
 * @param resourcePath 资源路径
 */
export function getResourceApi(resourcePath: string) {
  return request({
    url: getResourceUrl(resourcePath),
    method: "get",
    responseType: 'blob'
  })
}
