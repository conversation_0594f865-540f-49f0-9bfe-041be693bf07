# HTTP server (可选：重定向到 HTTPS)
server {
    listen 80;
    server_name dolphin-ai.cn;

    location / {
        return 301 https://$host$request_uri;
    }

    location /api {
        return 301 https://$host$request_uri;
    }
}

# HTTPS server
server {
    listen 443 ssl;
    server_name dolphin-ai.cn;

    ssl_certificate /etc/nginx/ssl/pacs.dolphin-ai.cn/fullchain.pem;
    ssl_certificate_key /etc/nginx/ssl/pacs.dolphin-ai.cn/privkey.pem;

    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;

    root /usr/share/nginx/html/;
    resolver *************** valid=30s;

    index index.html;

    location /api {
        proxy_pass http://dolphin-api:14580;
        proxy_http_version 1.1;
        proxy_buffering off;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        add_header Cache-Control "no-cache, no-store, must-revalidate" always;
        add_header Pragma "no-cache" always;
        add_header X-Accel-Buffering "no";
    }
}