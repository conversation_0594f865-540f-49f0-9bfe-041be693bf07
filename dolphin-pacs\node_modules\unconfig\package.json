{"name": "unconfig", "type": "module", "version": "7.3.2", "description": "A universal solution for loading configurations.", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/antfu/unconfig#readme", "repository": {"type": "git", "url": "git+https://github.com/antfu/unconfig.git"}, "bugs": {"url": "https://github.com/antfu/unconfig/issues"}, "keywords": ["config"], "sideEffects": false, "exports": {".": "./dist/index.mjs", "./presets": "./dist/presets.mjs"}, "main": "./dist/index.mjs", "module": "./dist/index.mjs", "types": "./dist/index.d.mts", "files": ["*.d.ts", "dist"], "dependencies": {"@quansync/fs": "^0.1.1", "defu": "^6.1.4", "jiti": "^2.4.2", "quansync": "^0.2.8"}, "devDependencies": {"@antfu/eslint-config": "^4.8.1", "@antfu/ni": "^23.3.1", "@antfu/utils": "^9.1.0", "@types/node": "^22.13.10", "bumpp": "^10.0.3", "eslint": "^9.22.0", "lodash-es": "^4.17.21", "tsx": "^4.19.3", "typescript": "^5.8.2", "unbuild": "^3.5.0", "unplugin-quansync": "^0.3.4", "vitest": "^3.0.8"}, "scripts": {"build": "unbuild", "dev": "unbuild --stub", "lint": "eslint", "release": "bumpp --commit --push --tag && pnpm publish", "start": "tsx src/index.ts", "test": "vitest", "typecheck": "tsc"}}