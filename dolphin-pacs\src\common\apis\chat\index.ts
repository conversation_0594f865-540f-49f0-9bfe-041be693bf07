import type * as Chat from "./type"
import { getToken } from "@@/utils/cache/cookies"
import axios from "axios"

/**
 * 发送聊天消息到AI助手（流式接口）
 * @param data 聊天请求数据
 * @returns AI回复文本
 */
export function sendChatMessageApi(data: Chat.ChatRequestData) {
  const token = getToken()

  return axios({
    url: `${import.meta.env.VITE_BASE_URL}/pacs/chat`,
    method: "post",
    data,
    headers: {
      "Authorization": token ? `Bearer ${token}` : undefined,
      "Content-Type": "application/json"
    },
    timeout: 50000,
    withCredentials: false
  }).then(response => response.data)
}
