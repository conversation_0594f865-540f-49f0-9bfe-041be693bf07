<script lang="ts" setup>
import ImageListItem from './ImageListItem.vue'

defineOptions({
  name: "ImageList"
})

interface ImageData {
  id: string
  name: string
  size: string
  format: string
  uploadTime: string
  thumbnail: string
  fabricObject?: any
}

interface Props {
  images: ImageData[]
  selectedId: string
}

defineProps<Props>()

const emit = defineEmits<{
  select: [id: string]
  delete: [id: string]
  toggleVisibility: [id: string]
  analyze: [id: string]
}>()
</script>

<template>
  <div class="image-list">
    <h5>图像列表</h5>
    
    <div v-if="images.length === 0" class="empty-list">
      暂无图像
    </div>
    
    <div v-else class="list-items">
      <ImageListItem
        v-for="image in images"
        :key="image.id"
        :image="image"
        :is-selected="image.id === selectedId"
        @select="emit('select', image.id)"
        @delete="emit('delete', image.id)"
        @toggle-visibility="emit('toggleVisibility', image.id)"
        @analyze="emit('analyze', image.id)"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.image-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  h5 {
    margin: 0;
    padding: 12px 16px 8px;
    font-size: 14px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  .empty-list {
    padding: 32px 16px;
    text-align: center;
    color: var(--el-text-color-placeholder);
    font-size: 14px;
  }

  .list-items {
    flex: 1;
    overflow-y: auto;
    padding: 0 8px 8px;
  }
}
</style>
