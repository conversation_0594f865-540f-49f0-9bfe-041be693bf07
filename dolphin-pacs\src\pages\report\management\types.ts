/** 报告信息接口 */
export interface ReportInfo {
  /** 报告ID */
  id: string
  /** 报告号 */
  reportNumber: string
  /** 患者姓名 */
  patientName: string
  /** 性别 */
  gender: '男' | '女'
  /** 年龄 */
  age: number
  /** 报告标题 */
  reportTitle: string
  /** 检查时间 */
  examTime: string
  /** 科室 */
  department: string
  /** 状态 */
  status: '待写报告' | '质控中' | '已完成'
  /** 创建时间 */
  createTime: string
  /** 更新时间 */
  updateTime: string
  /** 报告内容 */
  content?: string
  /** 诊断结果 */
  diagnosis?: string
  /** 建议 */
  suggestion?: string
}

/** 报告筛选参数 */
export interface ReportFilterParams {
  /** 搜索关键词 */
  keyword?: string
  /** 科室 */
  department?: string
  /** 状态 */
  status?: string
  /** 开始时间 */
  startTime?: string
  /** 结束时间 */
  endTime?: string
}

/** 报告分页查询参数 */
export interface ReportPageParams extends ReportFilterParams {
  /** 当前页码 */
  currentPage: number
  /** 每页大小 */
  pageSize: number
}

/** 报告分页响应数据 */
export interface ReportPageResponse {
  /** 报告列表 */
  list: ReportInfo[]
  /** 总数 */
  total: number
  /** 当前页码 */
  currentPage: number
  /** 每页大小 */
  pageSize: number
}

/** 科室选项 */
export interface DepartmentOption {
  /** 科室代码 */
  value: string
  /** 科室名称 */
  label: string
}

/** 状态选项 */
export interface StatusOption {
  /** 状态值 */
  value: string
  /** 状态名称 */
  label: string
  /** 标签类型 */
  type: 'warning' | 'primary' | 'success' | 'info' | 'danger'
}
