<script lang="ts" setup>
defineOptions({
  name: "GridToggle"
})

interface Props {
  showGrid: boolean
}

defineProps<Props>()

const emit = defineEmits<{
  toggle: []
}>()
</script>

<template>
  <div class="grid-toggle">
    <el-button
      :type="showGrid ? 'primary' : 'default'"
      size="small"
      @click="emit('toggle')"
    >
      {{ showGrid ? '隐藏网格' : '显示网格' }}
    </el-button>
  </div>
</template>

<style lang="scss" scoped>
.grid-toggle {
  // 样式可以根据需要添加
}
</style>
