<script lang="ts" setup>
import { Loading } from "@element-plus/icons-vue"

defineOptions({
  name: "LoadingOverlay"
})

interface Props {
  text?: string
}

withDefaults(defineProps<Props>(), {
  text: '画布加载中...'
})
</script>

<template>
  <div class="loading-overlay">
    <el-icon class="is-loading">
      <Loading />
    </el-icon>
    <p>{{ text }}</p>
  </div>
</template>

<style lang="scss" scoped>
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;

  p {
    margin-top: 16px;
    color: var(--el-text-color-regular);
    font-size: 14px;
  }
}
</style>
