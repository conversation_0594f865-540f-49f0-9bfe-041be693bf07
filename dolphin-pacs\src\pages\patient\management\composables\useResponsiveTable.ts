import { ref, onMounted, onUnmounted } from "vue"

/**
 * 响应式表格 Composable
 * 根据屏幕尺寸动态调整表格列的显示
 */
export function useResponsiveTable() {
  // 屏幕宽度
  const screenWidth = ref(window.innerWidth)
  
  // 是否为移动端
  const isMobile = ref(false)
  
  // 是否为平板端
  const isTablet = ref(false)
  
  // 是否为桌面端
  const isDesktop = ref(false)

  // 更新屏幕尺寸状态
  const updateScreenSize = () => {
    screenWidth.value = window.innerWidth
    isMobile.value = screenWidth.value < 768
    isTablet.value = screenWidth.value >= 768 && screenWidth.value < 1200
    isDesktop.value = screenWidth.value >= 1200
  }

  // 监听窗口大小变化
  const handleResize = () => {
    updateScreenSize()
  }

  // 获取响应式列配置
  const getResponsiveColumns = () => {
    const baseColumns = [
      {
        type: "checkbox" as const,
        width: "60px",
        minWidth: "60px"
      },
      {
        field: "name",
        title: "姓名",
        width: "120px",
        minWidth: "100px",
        fixed: "left" as const
      },
      {
        field: "gender",
        title: "性别",
        width: "80px",
        minWidth: "70px",
        slots: {
          default: "gender-column"
        }
      },
      {
        field: "age",
        title: "年龄",
        width: "80px",
        minWidth: "60px"
      },
      {
        field: "examType",
        title: "检查项目",
        minWidth: "150px",
        slots: {
          default: "exam-type-column"
        }
      },
      {
        field: "examTime",
        title: "检查时间",
        width: "160px",
        minWidth: "140px"
      },
      {
        field: "department",
        title: "科室",
        width: "120px",
        minWidth: "100px"
      },
      {
        field: "status",
        title: "状态",
        width: "100px",
        minWidth: "80px",
        slots: {
          default: "status-column"
        }
      },
      {
        title: "操作",
        width: "120px",
        minWidth: "100px",
        fixed: "right" as const,
        showOverflow: false,
        slots: {
          default: "row-operate"
        }
      }
    ]

    // 根据屏幕尺寸过滤列
    if (isMobile.value) {
      // 移动端只显示核心列
      return baseColumns.filter(col => 
        col.type === "checkbox" || 
        col.field === "name" || 
        col.field === "gender" ||
        col.field === "examType" ||
        col.field === "status" ||
        col.title === "操作"
      )
    } else if (isTablet.value) {
      // 平板端隐藏年龄列
      return baseColumns.filter(col => col.field !== "age")
    }

    // 桌面端显示所有列
    return baseColumns
  }

  onMounted(() => {
    updateScreenSize()
    window.addEventListener("resize", handleResize)
  })

  onUnmounted(() => {
    window.removeEventListener("resize", handleResize)
  })

  return {
    screenWidth,
    isMobile,
    isTablet,
    isDesktop,
    getResponsiveColumns
  }
}
