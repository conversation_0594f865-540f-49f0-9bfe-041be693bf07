<script lang="ts" setup>
defineOptions({
  name: "PanelHeader"
})

interface Props {
  imageCount: number
}

defineProps<Props>()
</script>

<template>
  <div class="panel-header">
    <h4>图像信息</h4>
    <span class="image-count">{{ imageCount }} 张图像</span>
  </div>
</template>

<style lang="scss" scoped>
.panel-header {
  padding: 16px;
  border-bottom: 1px solid var(--el-border-color-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--el-bg-color-page);

  h4 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }

  .image-count {
    font-size: 12px;
    color: var(--el-text-color-regular);
    background: var(--el-color-primary-light-9);
    padding: 2px 8px;
    border-radius: 12px;
  }
}
</style>
