<script lang="ts" setup>
import { ref, reactive, watch, onMounted, useTemplateRef } from "vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import type { VxeGridInstance, VxeGridProps } from "vxe-table";
import type { PatientInfo, PatientFilterParams } from "./types";
import { Search, Refresh } from "@element-plus/icons-vue";
import {
  departmentOptions,
  statusOptions,
  getStatusTagType,
} from "./data/mockData";
import { useResponsiveTable } from "./composables/useResponsiveTable";
import { getPatientSearchApi } from "@/common/apis/patients";
import {
  transformFilterParams,
  transformPageResponse,
} from "./utils/dataTransform";

defineOptions({
  // 命名当前组件
  name: "PatientManagement",
});

const router = useRouter();

// #region vxe-grid
const xGridDom = useTemplateRef<VxeGridInstance>("xGridDom");

// 响应式表格
const { getResponsiveColumns, screenWidth } = useResponsiveTable();

// 监听屏幕尺寸变化，更新表格列配置
watch(screenWidth, () => {
  if (xGridOpt.columns) {
    xGridOpt.columns = getResponsiveColumns();
  }
});

// 筛选表单数据
const filterForm = reactive<PatientFilterParams>({
  name: "",
  department: "",
  status: "",
  startTime: "",
  endTime: "",
});

// 时间范围选择 - 移除 value-format，让组件返回 Date 对象，然后转换为时间戳
const timeRange = ref<[Date, Date] | []>([]);

// 监听时间范围变化
watch(timeRange, (newRange: [Date, Date] | []) => {
  if (newRange && newRange.length === 2) {
    // 转换为时间戳（毫秒）
    const startTimestamp = newRange[0].getTime().toString();
    const endTimestamp = newRange[1].getTime().toString();

    filterForm.startTime = startTimestamp;
    filterForm.endTime = endTimestamp;
  } else {
    filterForm.startTime = "";
    filterForm.endTime = "";
  }
});

const xGridOpt: VxeGridProps = reactive({
  loading: false,
  autoResize: true,
  /** 分页配置项 */
  pagerConfig: {
    align: "right",
  },
  /** 禁用表单配置项，使用自定义筛选区域 */
  /** 工具栏配置 */
  toolbarConfig: {
    refresh: true,
    custom: true,
    slots: {
      buttons: "toolbar-btns",
    },
  },
  /** 自定义列配置项 */
  customConfig: {
    /** 是否允许列选中  */
    checkMethod: ({ column }: { column: any }) =>
      !["name"].includes(column.field),
  },
  /** 列配置 */
  columns: [],
  /** 数据代理配置项（基于 Promise API） */
  proxyConfig: {
    /** 启用动态序号代理 */
    seq: true,
    /** 是否自动加载，默认为 true */
    autoLoad: true,
    props: {
      total: "total",
    },
    ajax: {
      query: ({ page }: { page: any }) => {
        xGridOpt.loading = true;
        return new Promise((resolve) => {
          // 使用筛选条件
          const params: PatientFilterParams = {
            name: filterForm.name || "",
            department: filterForm.department || "",
            status: filterForm.status || "",
            startTime: filterForm.startTime,
            endTime: filterForm.endTime,
            size: page.pageSize,
            currentPage: page.currentPage,
          };

          loadPatientData(params)
            .then((res) => {
              xGridOpt.loading = false;
              resolve(res);
            })
            .catch(() => {
              xGridOpt.loading = false;
              resolve({ total: 0, result: [] });
            });
        });
      },
    },
  },
});

// 患者数据加载函数
const loadPatientData = async (params: PatientFilterParams) => {
  try {
    // 转换前端参数为接口参数
    const apiParams = transformFilterParams(params);

    // 调用患者搜索接口
    const response = await getPatientSearchApi(apiParams);

    // 转换接口响应为前端期望的格式
    return transformPageResponse(response.data.records, response.data.totalRow);
  } catch (error) {
    console.error("获取患者数据失败:", error);
    ElMessage.error("获取患者数据失败，请稍后重试");
    return {
      total: 0,
      result: [],
    };
  }
};

// 查询数据
const handleQuery = () => {
  xGridDom.value?.commitProxy("query");
};

// 重置筛选
const handleReset = () => {
  Object.assign(filterForm, {
    name: "",
    department: "",
    status: "",
    startTime: "",
    endTime: "",
  });
  timeRange.value = [];
  handleQuery();
};

// 查看详情
const handleViewDetail = (row: PatientInfo) => {
  router.push(`/patient/detail/${row.id}`);
};

onMounted(() => {
  // 设置响应式列配置
  xGridOpt.columns = getResponsiveColumns();
  // 初始化加载数据
  handleQuery();
});
</script>

<template>
  <div class="patient-management">
    <!-- 筛选区域 -->
    <div class="filter-section">
      <el-row :gutter="20">
        <el-col :xl="4" :lg="5" :md="6" :sm="12" :xs="24">
          <el-form-item label="患者姓名">
            <el-input
              v-model="filterForm.name"
              placeholder="请输入患者姓名"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :xl="3" :lg="4" :md="5" :sm="12" :xs="24">
          <el-form-item label="科室">
            <el-select
              v-model="filterForm.department"
              placeholder="全部科室"
              clearable
            >
              <el-option
                v-for="dept in departmentOptions"
                :key="dept.value"
                :label="dept.label"
                :value="dept.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xl="3" :lg="4" :md="5" :sm="12" :xs="24">
          <el-form-item label="状态">
            <el-select
              v-model="filterForm.status"
              placeholder="全部状态"
              clearable
            >
              <el-option
                v-for="status in statusOptions"
                :key="status.value"
                :label="status.label"
                :value="status.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :xl="6" :lg="7" :md="8" :sm="24" :xs="24">
          <el-form-item label="检查时间">
            <el-date-picker
              v-model="timeRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-col>
        <el-col :xl="8" :lg="4" :md="24" :sm="24" :xs="24">
          <el-form-item class="button-group">
            <el-button type="primary" @click="handleQuery">
              <el-icon><Search /></el-icon>
              查询
            </el-button>
            <el-button @click="handleReset">
              <el-icon><Refresh /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </div>

    <!-- 表格区域 -->
    <div class="table-section">
      <vxe-grid ref="xGridDom" v-bind="xGridOpt">
        <!-- 工具栏按钮 -->
        <template #toolbar-btns>
          <vxe-button status="primary" icon="vxe-icon-add">
            新增患者
          </vxe-button>
          <vxe-button status="danger" icon="vxe-icon-delete">
            批量删除
          </vxe-button>
        </template>

        <!-- 性别列 -->
        <template #gender-column="{ row, column }">
          <el-tag
            :type="row[column.field] === '男' ? 'primary' : 'danger'"
            effect="light"
            size="small"
          >
            {{ row[column.field] }}
          </el-tag>
        </template>

        <!-- 检查项目列 -->
        <template #exam-type-column="{ row, column }">
          <el-tooltip
            :content="row[column.field]"
            placement="top"
            :disabled="row[column.field].length <= 8"
          >
            <span class="exam-type-text">
              {{ row[column.field] }}
            </span>
          </el-tooltip>
        </template>

        <!-- 状态列 -->
        <template #status-column="{ row, column }">
          <el-tag :type="getStatusTagType(row[column.field])" effect="plain">
            {{ row[column.field] }}
          </el-tag>
        </template>

        <!-- 操作列 -->
        <template #row-operate="{ row }">
          <el-button link type="primary" @click="handleViewDetail(row)">
            查看详情
          </el-button>
        </template>
      </vxe-grid>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.patient-management {
  padding: 20px;

  .filter-section {
    background: var(--el-bg-color);
    padding: 24px;
    border-radius: 8px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .el-form-item {
      margin-bottom: 0;

      .el-form-item__label {
        font-weight: 500;
        color: var(--el-text-color-regular);
      }
    }

    .el-input,
    .el-select,
    .el-date-picker {
      width: 100%;
    }

    .el-button {
      margin-right: 12px;

      &:last-child {
        margin-right: 0;
      }
    }

    .button-group {
      .el-form-item__content {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
      }
    }
  }

  .table-section {
    background: var(--el-bg-color);
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    overflow: hidden;

    // 表格自适应样式
    :deep(.vxe-table) {
      .vxe-body--column {
        // 检查项目列文本样式
        .exam-type-text {
          display: block;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 100%;
        }
      }
    }
  }

  // 响应式设计
  @media screen and (max-width: 1200px) {
    .filter-section {
      .el-row {
        .el-col {
          margin-bottom: 16px;
        }
      }
    }
  }

  @media screen and (max-width: 768px) {
    padding: 10px;

    .filter-section {
      padding: 16px;

      .el-row {
        .el-col {
          &:not(:last-child) {
            margin-bottom: 16px;
          }
        }
      }

      .el-button {
        width: 100%;
        margin-right: 0;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .table-section {
      :deep(.vxe-grid) {
        // 移动端隐藏部分列
        .vxe-header--column,
        .vxe-body--column {
          &[data-colid="age"],
          &[data-colid="department"] {
            display: none;
          }
        }
      }
    }
  }

  @media screen and (max-width: 480px) {
    .table-section {
      :deep(.vxe-grid) {
        // 超小屏幕进一步隐藏列
        .vxe-header--column,
        .vxe-body--column {
          &[data-colid="examTime"] {
            display: none;
          }
        }
      }
    }
  }
}
</style>
