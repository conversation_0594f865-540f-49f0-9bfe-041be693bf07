import type { ReportInfo, DepartmentOption, StatusOption } from '../types'

/** 科室选项 */
export const departmentOptions: DepartmentOption[] = [
  { value: '', label: '全部科室' },
  { value: '心内科', label: '心内科' },
  { value: '心外科', label: '心外科' },
  { value: '神经内科', label: '神经内科' },
  { value: '神经外科', label: '神经外科' },
  { value: '消化内科', label: '消化内科' },
  { value: '呼吸内科', label: '呼吸内科' },
  { value: '内分泌科', label: '内分泌科' },
  { value: '肾内科', label: '肾内科' },
  { value: '血液科', label: '血液科' },
  { value: '肿瘤科', label: '肿瘤科' },
  { value: '妇产科', label: '妇产科' },
  { value: '儿科', label: '儿科' },
  { value: '急诊科', label: '急诊科' },
  { value: 'ICU', label: 'ICU' }
]

/** 状态选项 */
export const statusOptions: StatusOption[] = [
  { value: '', label: '全部状态', type: 'info' },
  { value: '待写报告', label: '待写报告', type: 'warning' },
  { value: '质控中', label: '质控中', type: 'primary' },
  { value: '已完成', label: '已完成', type: 'success' }
]

/** 获取状态标签类型 */
export const getStatusTagType = (status: string): 'warning' | 'primary' | 'success' | 'info' | 'danger' => {
  const statusOption = statusOptions.find(option => option.value === status)
  return statusOption?.type || 'info'
}

/** 模拟报告数据 */
export const mockReportData: ReportInfo[] = [
  {
    id: '1',
    reportNumber: '00001',
    patientName: '张三',
    gender: '男',
    age: 45,
    reportTitle: '心脏超声',
    examTime: '2025.07.22',
    department: '心内科',
    status: '待写报告',
    createTime: '2025-07-22 09:30:00',
    updateTime: '2025-07-22 09:30:00',
    content: '心脏超声检查显示：左心房内径正常，左心室内径正常，室间隔厚度正常，左心室后壁厚度正常。',
    diagnosis: '心脏结构正常',
    suggestion: '建议定期复查'
  },
  {
    id: '2',
    reportNumber: '00002',
    patientName: '李四',
    gender: '女',
    age: 38,
    reportTitle: '腹部超声',
    examTime: '2025.07.22',
    department: '消化内科',
    status: '质控中',
    createTime: '2025-07-22 10:15:00',
    updateTime: '2025-07-22 14:20:00',
    content: '腹部超声检查显示：肝脏大小形态正常，胆囊壁光滑，胰腺回声均匀。',
    diagnosis: '腹部器官未见明显异常',
    suggestion: '注意饮食，定期体检'
  },
  {
    id: '3',
    reportNumber: '00003',
    patientName: '王五',
    gender: '男',
    age: 52,
    reportTitle: '颈动脉超声',
    examTime: '2025.07.22',
    department: '神经内科',
    status: '已完成',
    createTime: '2025-07-22 11:00:00',
    updateTime: '2025-07-22 16:45:00',
    content: '颈动脉超声检查显示：双侧颈总动脉内膜光滑，血流通畅，未见明显狭窄。',
    diagnosis: '颈动脉未见明显异常',
    suggestion: '控制血压，低盐饮食'
  },
  {
    id: '4',
    reportNumber: '00004',
    patientName: '赵六',
    gender: '女',
    age: 29,
    reportTitle: '甲状腺超声',
    examTime: '2025.07.21',
    department: '内分泌科',
    status: '已完成',
    createTime: '2025-07-21 14:30:00',
    updateTime: '2025-07-21 17:20:00',
    content: '甲状腺超声检查显示：甲状腺大小正常，回声均匀，未见结节。',
    diagnosis: '甲状腺正常',
    suggestion: '保持良好生活习惯'
  },
  {
    id: '5',
    reportNumber: '00005',
    patientName: '孙七',
    gender: '男',
    age: 67,
    reportTitle: '肾脏超声',
    examTime: '2025.07.21',
    department: '肾内科',
    status: '待写报告',
    createTime: '2025-07-21 15:45:00',
    updateTime: '2025-07-21 15:45:00',
    content: '肾脏超声检查显示：双肾大小正常，皮质回声正常，未见积水。',
    diagnosis: '双肾未见明显异常',
    suggestion: '多饮水，定期复查'
  }
]

/** 生成更多模拟数据 */
export const generateMockReportData = (count: number = 50): ReportInfo[] => {
  const names = ['张三', '李四', '王五', '赵六', '孙七', '周八', '吴九', '郑十', '钱一', '陈二']
  const titles = ['心脏超声', '腹部超声', '颈动脉超声', '甲状腺超声', '肾脏超声', '肝脏超声', '胆囊超声', '脾脏超声']
  const departments = ['心内科', '消化内科', '神经内科', '内分泌科', '肾内科', '心外科', '神经外科', '呼吸内科']
  const statuses: Array<'待写报告' | '质控中' | '已完成'> = ['待写报告', '质控中', '已完成']
  const genders: Array<'男' | '女'> = ['男', '女']

  const data: ReportInfo[] = []
  
  for (let i = 0; i < count; i++) {
    const id = String(i + 1)
    const reportNumber = String(i + 1).padStart(5, '0')
    const name = names[Math.floor(Math.random() * names.length)]
    const gender = genders[Math.floor(Math.random() * genders.length)]
    const age = Math.floor(Math.random() * 60) + 20
    const title = titles[Math.floor(Math.random() * titles.length)]
    const department = departments[Math.floor(Math.random() * departments.length)]
    const status = statuses[Math.floor(Math.random() * statuses.length)]
    
    // 生成随机日期
    const baseDate = new Date('2025-07-20')
    const randomDays = Math.floor(Math.random() * 5)
    const examDate = new Date(baseDate.getTime() + randomDays * 24 * 60 * 60 * 1000)
    const examTime = examDate.toLocaleDateString('zh-CN').replace(/\//g, '.')
    
    const createTime = examDate.toISOString().slice(0, 19).replace('T', ' ')
    const updateTime = status === '待写报告' ? createTime : 
      new Date(examDate.getTime() + Math.random() * 8 * 60 * 60 * 1000).toISOString().slice(0, 19).replace('T', ' ')

    data.push({
      id,
      reportNumber,
      patientName: name,
      gender,
      age,
      reportTitle: title,
      examTime,
      department,
      status,
      createTime,
      updateTime,
      content: `${title}检查显示：检查结果正常，未见明显异常。`,
      diagnosis: '未见明显异常',
      suggestion: '建议定期复查'
    })
  }
  
  return data
}
