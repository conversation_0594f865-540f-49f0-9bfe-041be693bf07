import type * as Patients from "./type"
import { request } from "@/http/axios"

/**
 * 分页查询患者列表
 * @param params 查询参数
 * @returns 分页患者数据
 */
export function getPatientPageApi(params: Patients.PatientPageParams) {
  return request<Patients.PatientPageApiResponse>({
    url: "pacs/patient/page",
    method: "get",
    params
  })
}

/**
 * 搜索患者列表
 * @param params 搜索参数
 * @returns 搜索结果数据
 */
export function getPatientSearchApi(params: Patients.PatientSearchParams) {
  return request<Patients.PatientSearchApiResponse>({
    url: "pacs/patient/search",
    method: "get",
    params
  })
}

/**
 * 获取所有患者列表（无分页）
 * @returns 所有患者数据
 */
export function getPatientListApi() {
  return request<Patients.PatientListApiResponse>({
    url: "pacs/patient/list",
    method: "get"
  })
}

/**
 * 获取患者详情信息
 * @param id 患者ID
 * @returns 患者详情数据
 */
export function getPatientInfoApi(id: string | number) {
  return request<Patients.PatientDetailApiResponse>({
    url: "pacs/patient/info",
    method: "get",
    params: { id }
  })
}
